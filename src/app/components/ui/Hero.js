'use client';
import { getTranslation } from '@/lib/i18n';
import { <PERSON>, CardBody, CardHeader, CardFooter, Input, Button, addToast } from '@heroui/react';
import { useState, useRef } from 'react';
import OriginalStringArtAlgorithm from '@/lib/originalAlgorithm';

export default function Hero({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const [numberOfPins, setNumberOfPins] = useState(288);
  const [maxLines, setMaxLines] = useState(4000);
  const [lineWeight, setLineWeight] = useState(20);
  const [uploadedImage, setUploadedImage] = useState(null);
  const fileInputRef = useRef(null);
  const [currentImage, setCurrentImage] = useState(null);
  const [progress, setProgress] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [numberOfLines, setNumberOfLines] = useState(0);
  const [threadLength, setThreadLength] = useState(0);
  const [processTime, setProcessTime] = useState(0);
  const [pinSequence, setPinSequence] = useState('');


  const inputCanvasRef = useRef(null);
  const outputCanvasRef = useRef(null);

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          setUploadedImage(img);
          drawImageToCanvas(img);
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          setUploadedImage(img);
          drawImageToCanvas(img);
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const handleRemoveImage = () => {
    setUploadedImage(null);
    setCurrentImage(null);
    const canvas = inputCanvasRef.current;
    if (canvas) {
      const ctx = canvas.getContext('2d');
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    }
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const drawImageToCanvas = (img) => {
    const canvas = inputCanvasRef.current;
    if (canvas) {
      const inputCtx = canvas.getContext('2d');
      const size = Math.min(canvas.width, canvas.height);

      // Clear the canvas
      inputCtx.clearRect(0, 0, canvas.width, canvas.height);

      // 计算居中裁剪
      const scale = Math.min(img.width, img.height);
      const x = (img.width - scale) / 2;
      const y = (img.height - scale) / 2;

      inputCtx.drawImage(img, x, y, scale, scale, 0, 0, size, size);

      // 应用圆形遮罩
      inputCtx.globalCompositeOperation = 'destination-in';
      inputCtx.beginPath();
      inputCtx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
      inputCtx.closePath();
      inputCtx.fill();
      inputCtx.globalCompositeOperation = 'source-over';

      setCurrentImage(inputCtx.getImageData(0, 0, size, size));
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleGenerate = async () => {
    if (!currentImage) {
      addToast({
        title: t('Warning'),
        message: t('Please upload an image first'),
        color: 'danger',
        hideCloseButton: true,
        shouldShowTimeoutProgress: true,
        variant: 'bordered',
      });
      return;
    }

    setIsGenerating(true);
    const startTime = Date.now();

    const outputCtx = outputCanvasRef.current.getContext('2d');

    try {
      // 创建算法实例
      const algorithm = new OriginalStringArtAlgorithm({
        imageSize: 500,
        nPins: numberOfPins,
        maxLines: maxLines,
        lineWeight: lineWeight,
        minDistance: 20,
        hoopDiameter: 0.625,
        scale: 20
      });

      // 生成字符串艺术
      const result = await algorithm.generate(currentImage, (progressData) => {
        // 更新进度条
        if (progressData.step === 'drawing-lines' && progressData.linesDrawn > 0) {
          const percentage = Math.round((progressData.linesDrawn / progressData.totalLines) * 100);
          setProgress(`${progressData.linesDrawn} lines drawn | ${percentage}% complete`);
        }

        // 如果是绘制线条阶段，实时更新输出画布
        if (progressData.step === 'drawing-lines' && progressData.linesDrawn % 100 === 0) {
          drawCurrentProgress(algorithm, outputCtx);
        }
      });

      const endTime = Date.now();
      const processingTime = (endTime - startTime) / 1000;

      setProgress(`${result.lineSequence.length - 1} lines drawn | 100% complete`);
      setNumberOfLines(result.lineSequence.length - 1);
      setThreadLength(result.threadLength.toFixed(2));
      setProcessTime(processingTime.toFixed(1));
      setPinSequence(result.lineSequence.join(','));

      // 绘制最终结果
      drawResult(outputCtx, result);
    } catch (error) {
      console.error('生成失败:', error);
      setProgress(t('Generation failed'));
      addToast({
        type: 'error',
        title: t('Error'),
        message: t('Failed to generate string art: ') + error.message
      });
    } finally {
      setIsGenerating(false);
    }
  }

  const drawCurrentProgress = (algorithm, outputCtx) => {
    if (!algorithm || !algorithm.line_sequence || !algorithm.pin_coords) return;

    const size = 500;
    // 与原版本一致：画布大小为 IMG_SIZE * 2
    outputCanvasRef.current.width = size * 2;
    outputCanvasRef.current.height = size * 2;

    // 清空画布（白色背景）
    outputCtx.fillStyle = 'white';
    outputCtx.fillRect(0, 0, size * 2, size * 2);

    // 与原版本完全一致的绘制参数
    outputCtx.strokeStyle = 'black';
    outputCtx.lineWidth = 0.3;  // 与原版本一致
    outputCtx.globalAlpha = 1.0;  // 与原版本一致，不使用透明度

    for (let i = 0; i < algorithm.line_sequence.length - 1; i++) {
      const pin1 = algorithm.pin_coords[algorithm.line_sequence[i]];
      const pin2 = algorithm.pin_coords[algorithm.line_sequence[i + 1]];

      outputCtx.beginPath();
      // 与原版本一致：坐标乘以2
      outputCtx.moveTo(pin1[0] * 2, pin1[1] * 2);
      outputCtx.lineTo(pin2[0] * 2, pin2[1] * 2);
      outputCtx.stroke();
    }
  }

  const drawResult = (outputCtx, result) => {
    if (!result || !result.lineSequence || !result.pinCoords) return;

    const size = 500;
    // 与原版本一致：画布大小为 IMG_SIZE * 2
    outputCanvasRef.current.width = size * 2;
    outputCanvasRef.current.height = size * 2;

    // 清空画布（白色背景）
    outputCtx.fillStyle = 'white';
    outputCtx.fillRect(0, 0, size * 2, size * 2);

    // 与原版本完全一致的绘制参数
    outputCtx.strokeStyle = 'black';
    outputCtx.lineWidth = 0.3;  // 与原版本一致
    outputCtx.globalAlpha = 1.0;  // 与原版本一致，不使用透明度

    for (let i = 0; i < result.lineSequence.length - 1; i++) {
      const pin1 = result.pinCoords[result.lineSequence[i]];
      const pin2 = result.pinCoords[result.lineSequence[i + 1]];

      outputCtx.beginPath();
      // 与原版本一致：坐标乘以2
      outputCtx.moveTo(pin1[0] * 2, pin1[1] * 2);
      outputCtx.lineTo(pin2[0] * 2, pin2[1] * 2);
      outputCtx.stroke();
    }
  }

  const handleDownloadPinSequence = () => {
    if (!pinSequence) {
      addToast({
        title: t('Warning'),
        message: t('No pin sequence to download'),
        color: 'danger',
        hideCloseButton: true,
        shouldShowTimeoutProgress: true,
        variant: 'bordered',
      });
      return;
    }

    const blob = new Blob([pinSequence], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'pin-sequence.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  const handleDownloadImage = () => {
    const canvas = outputCanvasRef.current;
    if (!canvas) {
      addToast({
        title: t('Warning'),
        message: t('No image to download'),
        color: 'danger',
        hideCloseButton: true,
        shouldShowTimeoutProgress: true,
        variant: 'bordered',
      });
      return;
    }

    const link = document.createElement('a');
    link.download = 'string-art.png';
    link.href = canvas.toDataURL();
    link.click();
  }

  return (
    <div className="text-center pt-16 pb-2">
      <h1 className="text-5xl font-bold text-primary mb-2">
        {t('String Art Generator')}
      </h1>
      <div className="flex flex-col lg:flex-row gap-6 mt-10 max-w-7xl mx-auto">
        <Card id="hero-input" className="flex-1 max-w-[678px] mx-auto lg:mx-0">
          <CardBody>
            {/* Image Upload Area */}
            {!uploadedImage ? (
              <div
                id="image-upload"
                className='border-2 border-dashed border-gray-300 rounded-lg w-80 h-32 mx-auto flex flex-col items-center justify-center cursor-pointer hover:border-primary transition-colors'
                onClick={handleUploadClick}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
              >
                <svg className="w-8 h-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p className="text-sm text-gray-500">{t('Click to upload or drag and drop')}</p>
              </div>
            ) : (
              <div className="relative">
                <canvas
                  ref={inputCanvasRef}
                  width="500"
                  height="500"
                  className="max-w-full h-auto mx-auto"
                ></canvas>
                <button
                  onClick={handleRemoveImage}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600 transition-colors"
                >
                  ×
                </button>
              </div>
            )}

            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />
          </CardBody>
          <CardFooter className="flex flex-col gap-2">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <Input label={t("Number of Pins")}
                defaultValue={288}
                value={numberOfPins}
                onChange={(e) => setNumberOfPins(e.target.value)}
              ></Input>
              <Input label={t("Number of Lines")}
                defaultValue={4000}
                value={maxLines}
                onChange={(e) => setMaxLines(e.target.value)}
              ></Input>
              <Input label={t("Line Weight")}
                defaultValue={20}
                value={lineWeight}
                onChange={(e) => setLineWeight(e.target.value)}
              ></Input>
            </div>
            {uploadedImage && (
              <Button
                className="w-full mt-4"
                color="primary"
                onPress={handleGenerate}
                isDisabled={isGenerating}
                isLoading={isGenerating}
              >
                {isGenerating ? t("Generating...") : t("Generate String Art")}
              </Button>
            )}
          </CardFooter>
        </Card>

        {/* Hero Output - Only show when generating or completed */}
        {(isGenerating || (!isGenerating && numberOfLines > 0)) && (
          <Card id="hero-output" className="flex-1 max-w-[678px] mx-auto lg:mx-0">
            <CardHeader>
              <div className='text-left'>
                <h3 className='text-2xl font-bold py-4'>{t('String Art Output:')}</h3>
                <p className='text-sm text-gray-500'>{progress}</p>
              </div>
            </CardHeader>
            <CardBody>
              <canvas
                ref={outputCanvasRef}
                width="500"
                height="500"
                className="max-w-full h-auto mx-auto"
              ></canvas>
            </CardBody>
            {/* Footer - Only show when generation is complete */}
            {!isGenerating && numberOfLines > 0 && (
              <CardFooter>
                <div className='flex flex-col w-full'>
                  <h3 className='text-2xl font-bold py-4 text-left'>{t('Result Information')}</h3>
                  <div className='flex items-center mb-2'>
                    <strong className="mr-2">{t('Number of Lines:')}</strong>
                    <p>{numberOfLines}</p>
                  </div>
                  <div className='flex items-center mb-2'>
                    <strong className="mr-2">{t('Thread Length:')}</strong>
                    <p>{threadLength}m</p>
                  </div>
                  <div className='flex items-center mb-2'>
                    <strong className="mr-2">{t('Processing Time:')}</strong>
                    <p>{processTime}s</p>
                  </div>
                  <div className='flex gap-2 mt-2'>
                    <Button onPress={handleDownloadPinSequence}>{t('Download Pin Sequence')}</Button>
                    <Button color="primary" onPress={handleDownloadImage}>{t('Download String Art Image')}</Button>
                  </div>
                </div>
              </CardFooter>
            )}
          </Card>
        )}
      </div>
    </div>
  );
}
